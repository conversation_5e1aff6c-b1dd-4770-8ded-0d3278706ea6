<template>
  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
    <h2 class="text-xl font-semibold mb-4">Thêm Mới Tài Sản</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <label class="block text-sm font-medium text-gray-700">Mã tài sản *</label>
        <input type="text" placeholder="Nhập mã tài sản" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Tên tài sản *</label>
        <input type="text" placeholder="Nhập tên tài sản" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Loại tài sản *</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn loại tài sản</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Đơn vị quản lý *</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn đơn vị quản lý</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Trạng thái</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn trạng thái</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Năm xây dựng *</label>
        <input type="text" placeholder="Nhập năm xây dựng" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Tỉnh/Thành phố *</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn tỉnh/thành phố</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Quận/Huyện *</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn quận/huyện</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Xã/Phường</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn xã/phường</option>
        </select>
      </div>
      <div class="md:col-span-3">
        <label class="block text-sm font-medium text-gray-700">Địa chỉ chi tiết</label>
        <input type="text" placeholder="Nhập địa chỉ chi tiết" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div class="md:col-span-3 grid grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700">Kinh độ (Longitude)</label>
          <input type="text" placeholder="Kinh độ (Longitude)" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Vĩ độ (Latitude)</label>
          <input type="text" placeholder="Vĩ độ (Latitude)" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
        </div>
      </div>
      <div class="md:col-span-3">
        <label class="block text-sm font-medium text-gray-700">Mô tả</label>
        <textarea placeholder="Nhập mô tả tài sản" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
      </div>
      <div class="md:col-span-3">
        <label class="block text-sm font-medium text-gray-700">Tài liệu đính kèm</label>
        <div class="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
          <div class="space-y-1 text-center">
            <Icon name="upload-cloud" class="mx-auto h-12 w-12 text-gray-400" />
            <div class="flex text-sm text-gray-600">
              <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                <span>Tải lên tài liệu</span>
                <input type="file" class="sr-only" />
              </label>
              <p class="pl-1">hoặc kéo thả vào đây</p>
            </div>
            <p class="text-xs text-gray-500">PNG, JPG, PDF tối đa 10MB</p>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-6 flex justify-end gap-3">
      <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
        Đặt lại
      </button>
      <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
        Thêm mới
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Icon from '@/components/Icon.vue'
</script>
