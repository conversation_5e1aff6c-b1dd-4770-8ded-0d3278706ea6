<script setup lang="ts">
import { ref, h } from 'vue';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Pencil, Trash2, Eye } from 'lucide-vue-next';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import DataTable from '@/components/DataTable.vue';
import ViewAssetDetailsModal from '@/components/modal/ViewAssetDetailsModal.vue';

// Define Props
const props = defineProps({
  features: {
    type: Array as () => Feature[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: true
  },
  error: {
    type: String as () => string | null,
    default: null
  },
  assetName: {
    type: String,
    default: 'Tài sản'
  }
});

// Component State for Modal
const isViewModalOpen = ref(false);
const selectedAssetIdForView = ref<string | number | null>(null);

const openViewModal = (assetId: string | number) => {
  selectedAssetIdForView.value = assetId;
  isViewModalOpen.value = true;
};

// const closeViewModal = () => (isViewModalOpen.value = false); // Không cần thiết nếu dùng v-model

// Action buttons component for cell rendering
const ActionButtons = (params: any) => {
  const eDiv = document.createElement('div');
  eDiv.className = 'flex gap-1';

  // View button
  /* const viewBtn = document.createElement('button');
  viewBtn.className = 'inline-flex items-center justify-center w-6 h-6 text-gray-500 hover:text-blue-600';
  viewBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>`;
  viewBtn.onclick = () => openViewModal(params.data.id); // Truyền ID của feature
  eDiv.appendChild(viewBtn); */

  // Edit button
  const editBtn = document.createElement('button');
  editBtn.className = 'inline-flex items-center justify-center w-6 h-6 text-gray-500 hover:text-green-600';
  editBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/></svg>`;
  editBtn.onclick = () => openViewModal(params.data.id);
  eDiv.appendChild(editBtn);

  // Delete button
  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'inline-flex items-center justify-center w-6 h-6 text-gray-500 hover:text-red-600';
  deleteBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>`;
  deleteBtn.onclick = () => console.log('Delete:', params.data);
  eDiv.appendChild(deleteBtn);

  return eDiv;
};

// Status cell component for rendering
const StatusCell = (params: any) => {
  const eSpan = document.createElement('span');
    const tinhtrang = params.value;
    const isNormal = tinhtrang === 'Bình thường' || tinhtrang === 'Tốt';
    const cellClass = isNormal ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
    eSpan.className = `px-2 py-1 text-xs font-semibold rounded-full ${cellClass}`;
  eSpan.innerText = tinhtrang;

  return eSpan;
}

// Defining types for our data
interface FeatureProperties {
  id_qt: string;
  ten: string;
  tenxa: string;
  nam_xd: number;
  nam_sd: number;
  dt_dat: number;
  quytrinh_vh: string;
  quytrinh_bt: string;
  tinhtrang: string;
  dv_quanly: string;
  phuongthuc: string;
  chuthich: string;
}

interface Feature {
  type: string;
  id: string;
  geometry: any;
  properties: FeatureProperties;
}

// Component State
const searchQuery = ref('');
const dataTableRef = ref<any>(null);

// Column Definitions for AG-Grid
const columnDefs = ref([
  {
    headerName: 'Hành động',
    cellRenderer: ActionButtons,
    width: 110,
    pinned: 'left',
    suppressMenu: true,
    sortable: false,
    filter: false,
    resizable: false,
  },
  {
    headerName: 'STT',
    valueGetter: 'node.rowIndex + 1',
    width: 70,
    suppressMenu: true,
    sortable: false,
    filter: false,
    resizable: false,
  },
  { headerName: 'Tên công trình', field: 'properties.ten', filter: true, minWidth: 250 },
  { headerName: 'Địa phương', field: 'properties.tenxa', filter: true, minWidth: 150 },
  { headerName: 'Năm sử dụng', field: 'properties.nam_sd', filter: 'agNumberColumnFilter', width: 120 },
  {
    headerName: 'Tình trạng',
    field: 'properties.tinhtrang',
    cellRenderer: StatusCell,
    filter: 'agTextColumnFilter',
    width: 150,
  },
  { headerName: 'Đơn vị quản lý', field: 'properties.dv_quanly', filter: true, minWidth: 200 },
  { headerName: 'Phương thức quản lý', field: 'properties.phuongthuc', filter: true, minWidth: 280 },
]);

// Search handler
const handleSearch = () => {
  if (dataTableRef.value) {
    dataTableRef.value.searchData(searchQuery.value);
  }
}

// Action handlers
const viewDetails = (data: Feature) => {
    console.log('View:', data);
};

const editItem = (data: Feature) => {
    console.log('Edit:', data);
};

const deleteItem = (data: Feature) => {
    console.log('Delete:', data);
};

</script>

<template>
  <div class="py-2 px-4 sm:px-6 sm:py-4 bg-white rounded-lg shadow-sm flex flex-col h-full">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
      <h2 class="text-lg font-semibold mb-2 sm:mb-0">Quản lý danh sách {{ assetName }}</h2>
      <div class="relative w-full sm:w-auto">
        <Search class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
        <Input v-model="searchQuery" @input="handleSearch" type="text" placeholder="Tìm kiếm..."
          class="pl-10 w-full sm:w-64" />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex-grow flex justify-center items-center">
      <LoadingSpinner />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex-grow flex justify-center items-center text-center text-red-600">
      <p><strong>Đã xảy ra lỗi:</strong> {{ error }}</p>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="!features.length" class="flex-grow flex justify-center items-center text-center text-gray-500">
        <p>Vui lòng chọn một loại tài sản từ thanh bên.</p>
    </div>

    <!-- Data Table -->
    <div v-else class="flex-grow">
      <DataTable ref="dataTableRef" :columnDefs="columnDefs" :rowData="features" :maxHeight="'calc(100vh - 320px)'" />
    </div>

    <!-- View Asset Details Modal -->
    <ViewAssetDetailsModal
      v-model:modelValue="isViewModalOpen"
      :assetId="selectedAssetIdForView"
    />
  </div>
</template>
