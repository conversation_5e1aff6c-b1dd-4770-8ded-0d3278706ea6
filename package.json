{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/leaflet": "^1.9.17", "@types/node": "^22.13.5", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tw-animate-css": "^1.2.5", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.0-beta.3", "@tailwindcss/vite": "^4.1.1", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^12.8.2", "ag-grid-community": "^33.2.4", "ag-grid-vue3": "^33.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "echarts": "^5.6.0", "laravel-vite-plugin": "^1.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lucide": "^0.468.0", "lucide-vue-next": "^0.468.0", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primevue": "^4.3.4", "reka-ui": "^2.2.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^4.1.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^6.2.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}