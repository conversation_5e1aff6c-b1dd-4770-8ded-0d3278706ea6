<?php

namespace App\Http\Requests\Taisan;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCongdapRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_qt'         => 'nullable|string|max:20',
            'id_xa'         => 'nullable|string|max:5',
            'ten'           => 'nullable|string|max:100',
            'quymo_ct'      => 'nullable|string|max:100',
            'loai_ct'       => 'nullable|string|max:5',
            'nam_xd'        => 'nullable|integer',
            'nam_sd'        => 'nullable|integer',
            'dt_dat'        => 'nullable|numeric',
            'tinhtrang'     => 'nullable|string|max:15',
            'quytrinh_vh'   => 'nullable|string|max:50',
            'quytrinh_bt'   => 'nullable|string|max:50',
            'dv_quanly'     => 'nullable|string|max:50',
            'phuongthuc'    => 'nullable|string|max:100',
            'chuthich'      => 'nullable|string',
            'geometry'      => ['nullable', 'array', function($attribute, $value, $fail) {
                if ($value && (!isset($value['type']) || !isset($value['coordinates']))) {
                    $fail('The geometry must be a valid GeoJSON object with type and coordinates.');
                }
            }]
        ];
    }

    public function messages(): array
    {
        return [
            'geometry.array' => 'Geometry phải là một GeoJSON object hợp lệ'
        ];
    }
}