<?php

namespace App\Repositories;

use App\Models\API\Taisan\Congdap;
use App\Repositories\Contracts\CongdapRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class CongdapRepository implements CongdapRepositoryInterface
{
    protected $model;
    protected $cachePrefix = 'congdap_';
    protected $cacheTTL = 3600; // 1 hour

    public function __construct(Congdap $model)
    {
        $this->model = $model;
    }

    public function paginate(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        // Lấy số trang hiện tại từ request
        $currentPage = request('page', 1);
        $cacheKey = $this->cachePrefix . 'list_' . md5(json_encode($filters) . $perPage . $currentPage);

        // Sử dụng hàm rememberWithKeyTracking nếu bạn đã triển khai nó
        // return $this->rememberWithKeyTracking($cacheKey, $this->cacheTTL, function () use ($filters, $perPage) { ... }, $this->cachePrefix . 'list_');

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($filters, $perPage) {
            // <<< THAY ĐỔI: Sử dụng Eager Loading >>>
            $query = $this->model->with(['xa', 'quyettoan']) // Tải relationship
                // <<< THAY ĐỔI: Select cột bảng chính + geom >>>
                ->selectRaw('taisan.congdap.*, ST_AsGeoJSON(taisan.congdap.geom) as geom');

            // <<< THAY ĐỔI: Bỏ alias 'cd' khi filter >>>
            if (isset($filters['id_xa'])) {
                $query->where('id_xa', $filters['id_xa']);
            }
            if (isset($filters['id_qt'])) {
                $query->where('id_qt', $filters['id_qt']);
            }
            if (isset($filters['loai_ct'])) {
                $query->where('loai_ct', $filters['loai_ct']);
            }

            if (isset($filters['search'])) {
                $searchTerm = trim($filters['search']);
                $query->where(function($q) use ($searchTerm) {
                    // <<< THAY ĐỔI: Bỏ alias 'cd.' >>>
                    $q->where('ten', 'ilike', '%'.$searchTerm.'%')
                      ->orWhere('quymo_ct', 'ilike', '%'.$searchTerm.'%')
                      // <<< THAY ĐỔI: Tìm trên relationship bằng orWhereHas >>>
                      ->orWhereHas('xa', function ($subQuery) use ($searchTerm) {
                          // <<< Đảm bảo cột 'tenxa' tồn tại trong bảng rg_xa >>>
                          $subQuery->where('tenxa', 'ilike', '%'.$searchTerm.'%');
                      })
                      ->orWhereHas('quyettoan', function ($subQuery) use ($searchTerm) {
                          // <<< Đảm bảo cột 'nguyengia' tồn tại trong bảng quyettoan >>>
                          // Cần cast sang text nếu nguyengia là kiểu số
                          $subQuery->whereRaw('CAST(nguyengia AS TEXT) ilike ?', ['%'.$searchTerm.'%']);
                      });
                });
            }

            return $query->paginate($perPage);
        });
    }

    /**
     * Phân trang chỉ lấy geometry và ID
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function paginateGeometryOnly(array $filters = []): LengthAwarePaginator
    {
        $cacheKey = $this->cachePrefix . 'geometry_list_' . md5(json_encode($filters));

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($filters) {
            $query = $this->model
                ->selectRaw('taisan.congdap.id, ST_AsGeoJSON(taisan.congdap.geom) as geom');

            // Áp dụng các bộ lọc
            if (isset($filters['id_xa'])) {
                $query->where('id_xa', $filters['id_xa']);
            }
            if (isset($filters['id_qt'])) {
                $query->where('id_qt', $filters['id_qt']);
            }
            if (isset($filters['loai_ct'])) {
                $query->where('loai_ct', $filters['loai_ct']);
            }

            if (isset($filters['search'])) {
                $searchTerm = trim($filters['search']);
                $query->where(function($q) use ($searchTerm) {
                    $q->where('ten', 'ilike', '%'.$searchTerm.'%')
                      ->orWhere('quymo_ct', 'ilike', '%'.$searchTerm.'%')
                      ->orWhereHas('xa', function ($subQuery) use ($searchTerm) {
                          $subQuery->where('tenxa', 'ilike', '%'.$searchTerm.'%');
                      })
                      ->orWhereHas('quyettoan', function ($subQuery) use ($searchTerm) {
                          $subQuery->whereRaw('CAST(nguyengia AS TEXT) ilike ?', ['%'.$searchTerm.'%']);
                      });
                });
            }

            // Không giới hạn số lượng bản ghi để lấy tất cả geometry
            return $query->paginate(1000);
        });
    }

    /**
     * Phân trang chỉ lấy thuộc tính (không lấy geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function paginateAttributesOnly(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        // Lấy số trang hiện tại từ request
        $currentPage = request('page', 1);
        $cacheKey = $this->cachePrefix . 'attributes_list_' . md5(json_encode($filters) . $perPage . $currentPage);

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($filters, $perPage) {
            $query = $this->model->with(['xa', 'quyettoan'])
                ->selectRaw('
                    taisan.congdap.id,
                    taisan.congdap.id_qt,
                    taisan.congdap.id_xa,
                    taisan.congdap.ten,
                    taisan.congdap.quymo_ct,
                    taisan.congdap.loai_ct,
                    taisan.congdap.nam_xd,
                    taisan.congdap.nam_sd,
                    taisan.congdap.dt_dat,
                    taisan.congdap.tinhtrang,
                    taisan.congdap.quytrinh_vh,
                    taisan.congdap.quytrinh_bt,
                    taisan.congdap.dv_quanly,
                    taisan.congdap.phuongthuc,
                    taisan.congdap.chuthich
                ');

            // Áp dụng các bộ lọc
            if (isset($filters['id_xa'])) {
                $query->where('id_xa', $filters['id_xa']);
            }
            if (isset($filters['id_qt'])) {
                $query->where('id_qt', $filters['id_qt']);
            }
            if (isset($filters['loai_ct'])) {
                $query->where('loai_ct', $filters['loai_ct']);
            }

            if (isset($filters['search'])) {
                $searchTerm = trim($filters['search']);
                $query->where(function($q) use ($searchTerm) {
                    $q->where('ten', 'ilike', '%'.$searchTerm.'%')
                      ->orWhere('quymo_ct', 'ilike', '%'.$searchTerm.'%')
                      ->orWhereHas('xa', function ($subQuery) use ($searchTerm) {
                          $subQuery->where('tenxa', 'ilike', '%'.$searchTerm.'%');
                      })
                      ->orWhereHas('quyettoan', function ($subQuery) use ($searchTerm) {
                          $subQuery->whereRaw('CAST(nguyengia AS TEXT) ilike ?', ['%'.$searchTerm.'%']);
                      });
                });
            }

            return $query->paginate($perPage);
        });
    }

    public function findById(string $id): ?Congdap
    {
        $cacheKey = $this->cachePrefix . $id;

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($id) {
            // <<< THAY ĐỔI: Sử dụng Eager Loading và find() >>>
             return $this->model->with(['xa', 'quyettoan']) // Tải relationship
                // <<< THAY ĐỔI: Select cột bảng chính + geom >>>
                ->selectRaw('taisan.congdap.*, ST_AsGeoJSON(taisan.congdap.geom) as geom')
                ->find($id); // Sử dụng find()
        });
    }

    public function create(array $data): Congdap
    {
        DB::beginTransaction();
        try {
            if (isset($data['geometry'])) {
                $data['geom'] = Congdap::geometryFromGeoJSON(json_encode($data['geometry']));
                unset($data['geometry']);
            }

            $created = $this->model->create($data); // Model event tự tạo ID

            // <<< THAY ĐỔI: Tải lại model với selectRaw cho geom >>>
            $reloadedCongdap = $this->model
                ->selectRaw('taisan.congdap.*, ST_AsGeoJSON(taisan.congdap.geom) as geom')
                ->find($created->id);

            DB::commit();
            $this->clearListCache();

            // Trả về model vừa tạo (Resource sẽ xử lý việc load relationship nếu cần)
            return $reloadedCongdap;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(string $id, array $data): ?Congdap
    {
         $this->clearCache($id);

        DB::beginTransaction();
        try {
            $congdap = $this->model->find($id);

            if (!$congdap) {
                DB::rollBack();
                return null;
            }

            if (isset($data['geometry'])) {
                $data['geom'] = Congdap::geometryFromGeoJSON(json_encode($data['geometry']));
                unset($data['geometry']);
            }

            $congdap->update($data);

            // <<< THAY ĐỔI: Tải lại model với selectRaw cho geom >>>
            $reloadedCongdap = $this->model
                ->selectRaw('taisan.congdap.*, ST_AsGeoJSON(taisan.congdap.geom) as geom')
                ->find($congdap->id); // Sử dụng $congdap->id thay vì $id để chắc chắn

            DB::commit();
            $this->clearListCache();

            // Trả về model vừa cập nhật
            return $reloadedCongdap;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }


    public function delete(string $id): bool
    {
        DB::beginTransaction();
        try {
            $deleted = $this->model->where('id', $id)->delete();

            DB::commit();

            if ($deleted) {
                $this->clearCache($id);
                $this->clearListCache();
            }

            return $deleted > 0;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function checkExists(string $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    protected function clearCache(string $id): void
    {
        Cache::forget($this->cachePrefix . $id);
    }

    protected function clearListCache(): void
    {
        // Giữ nguyên logic xóa cache list hiện tại
        // Hoặc triển khai Cache Tags / cơ chế quản lý key tốt hơn
        $keys = Cache::get('__keys__'.$this->cachePrefix . 'list_', []);
        foreach ($keys as $key) {
             Cache::forget($key);
        }
         Cache::forget('__keys__'.$this->cachePrefix . 'list_');
    }

     // Hàm rememberWithKeyTracking (nếu có) không cần thay đổi
     protected function rememberWithKeyTracking($key, $ttl, $callback, $listKeyPrefix = null)
     {
         $result = Cache::remember($key, $ttl, $callback);
         if ($listKeyPrefix) {
             $listKey = '__keys__' . $listKeyPrefix;
             $keys = Cache::get($listKey, []);
             if (!in_array($key, $keys)) {
                 $keys[] = $key;
                 Cache::put($listKey, $keys, $ttl + 60); // Lưu danh sách key lâu hơn một chút
             }
         }
         return $result;
     }
}
