<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import MainLayout from '@/layouts/MainLayout.vue'
import DataTable from '@/components/DataTable.vue'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import { Button } from '@/components/ui/button';
import { TableProperties, Search, GripHorizontal, ZoomIn } from 'lucide-vue-next';
import axios from 'axios'

const mapContainer = ref<HTMLDivElement | null>(null)
let map: L.Map | null = null
let baseLayers: Record<string, L.TileLayer> = {}
let drainageLayer: L.LayerGroup | null = null
let drainageMarkers: Record<string, L.Marker> = {}

const currentBase = ref('cartodb')
const showDrainageLayer = ref(false)
const showPumpStationLayer = ref(false)
const showDikeLayer = ref(false)
const showCanalLayer = ref(false)
const showOtherAssetsLayer = ref(false)

// Biến cho trạng thái loading
const isLoadingGeometry = ref(false)
const isLoadingAttributes = ref(false)

// Biến cho bảng dữ liệu
const showDataTable = ref(false)
const showSearchModal = ref(false)
const searchCriteria = ref({
  ten: '',
  quymo_ct: '',
  nam_xd: '',
  nguyengia: '',
  dt_dat: ''
})
const dataTableRef = ref<InstanceType<typeof DataTable> | null>(null)
const originalRowData = ref<any[]>([]) // Lưu trữ dữ liệu gốc để khôi phục sau khi tìm kiếm
const rowData = ref<any[]>([]) // Dữ liệu hiển thị trong bảng

// Biến cho modal draggable
const isDragging = ref(false)
const modalPosition = ref({ x: 0, y: 0 })
const dragStartPosition = ref({ x: 0, y: 0 })
const modalRef = ref<HTMLDivElement | null>(null)

// Định nghĩa cột cho bảng
const columnDefs = ref<any[]>([])

// Ánh xạ tên trường sang tên hiển thị tiếng Việt
const fieldNameMapping: Record<string, string> = {
  id: 'ID',
  id_qt: 'ID quản trị',
  id_xa: 'ID xã',
  ten: 'Tên',
  loai: 'Loại',
  loai_ct: 'Loại công trình',
  xa: 'Xã/Phường',
  tinhtrang: 'Tình trạng',
  quymo_ct: 'Quy mô',
  nam_xd: 'Năm xây dựng',
  nam_sd: 'Năm sử dụng',
  nguyengia: 'Nguyên giá (VNĐ)',
  dt_dat: 'Diện tích đất (m²)',
  quytrinh_vh: 'Quy trình vận hành',
  quytrinh_bt: 'Quy trình bảo trì',
  dv_quanly: 'Đơn vị quản lý',
  phuongthuc: 'Phương thức quản lý',
  chuthich: 'Chú thích'
}

// Cấu hình chiều rộng cho một số cột cụ thể
const columnWidths: Record<string, number> = {
  id: 80,
  ten: 400,
  loai: 100,
  loai_ct: 100,
  phuongthuc: 300
}

// Hàm tạo định nghĩa cột từ dữ liệu
function generateColumnDefs(data: any[]) {
  if (!data || data.length === 0) return []

  // Lấy mẫu dữ liệu đầu tiên để xác định các trường
  const sampleItem = data[0]

  // Tạo định nghĩa cột từ các trường của dữ liệu
  const columns = Object.keys(sampleItem).map(field => {
    // Bỏ qua trường coordinates vì nó được sử dụng cho bản đồ, không hiển thị trong bảng
    if (field === 'coordinates') return null

    return {
      field,
      headerName: fieldNameMapping[field] || field, // Sử dụng tên hiển thị từ mapping hoặc tên trường gốc
      sortable: true,
      filter: false,
      width: columnWidths[field] || undefined, // Sử dụng chiều rộng từ cấu hình nếu có
      suppressAutoSize: !!columnWidths[field] // Chỉ tắt auto-size nếu có chiều rộng cụ thể
    }
  }).filter(col => col !== null) // Loại bỏ các cột null

  return columns
}

// rowData đã được khai báo ở trên

// Hàm tải dữ liệu geometry từ API
async function fetchDrainageGeometry(filters = {}) {
  try {
    const response = await axios.get('/api/taisan/congdap/geometry', {
      params: filters
    })
    return response.data.data
  } catch (error) {
    console.error('Lỗi khi tải dữ liệu geometry cống đập:', error)
    return null
  }
}

// Hàm tải dữ liệu thuộc tính từ API
async function fetchDrainageAttributes(filters = {}, perPage = 1000) {
  try {
    const response = await axios.get('/api/taisan/congdap/attributes', {
      params: {
        ...filters,
        per_page: perPage
      }
    })
    return response.data
  } catch (error) {
    console.error('Lỗi khi tải dữ liệu thuộc tính cống đập:', error)
    return null
  }
}

// Biến lưu trữ dữ liệu geometry đã tải
let geometryData: any = null

// Hàm tải dữ liệu geometry cho bản đồ
async function loadGeometryData(filters = {}) {
  try {
    // Bắt đầu loading
    isLoadingGeometry.value = true

    // Tải dữ liệu geometry
    const geometryResponse = await fetchDrainageGeometry(filters)

    if (!geometryResponse) {
      console.error('Không thể tải dữ liệu geometry cống đập')
      return null
    }

    // Lưu lại dữ liệu geometry để sử dụng sau này
    geometryData = geometryResponse

    // Tạo dữ liệu GeoJSON đơn giản với thông tin tối thiểu cho popup
    return {
      type: 'FeatureCollection',
      features: geometryResponse.features.map((feature: any) => {
        // Đảm bảo properties có ít nhất id để tham chiếu sau này
        const id = feature.id || feature.properties?.id

        return {
          ...feature,
          properties: {
            ...feature.properties,
            id: id
          }
        }
      })
    }
  } catch (error) {
    console.error('Lỗi khi tải dữ liệu geometry:', error)
    return null
  } finally {
    // Kết thúc loading
    isLoadingGeometry.value = false
  }
}

// Hàm tải dữ liệu thuộc tính và cập nhật bảng
async function loadAttributesAndUpdateTable(filters = {}) {
  try {
    // Bắt đầu loading
    isLoadingAttributes.value = true

    // Kiểm tra xem đã có dữ liệu geometry chưa
    if (!geometryData) {
      console.error('Cần tải dữ liệu geometry trước')
      return false
    }

    // Tải dữ liệu thuộc tính
    const attributesResponse = await fetchDrainageAttributes(filters)

    if (!attributesResponse || !attributesResponse.data) {
      console.error('Không thể tải dữ liệu thuộc tính cống đập')
      return false
    }

    // Tạo map các thuộc tính theo ID để dễ truy cập
    const attributesMap = new Map()
    attributesResponse.data.forEach((item: any) => {
      attributesMap.set(item.id, item)
    })

    // Kết hợp dữ liệu geometry và thuộc tính
    const mappedData = geometryData.features.map((feature: any) => {
      const id = feature.id || feature.properties?.id
      const attributes = attributesMap.get(id) || {}

      // Lấy tọa độ từ geometry
      const coordinates = feature.geometry?.coordinates || [0, 0]

      return {
        id: id,
        ten: attributes.ten || '',
        loai: attributes.loai_ct || '',
        xa: attributes.tenxa || 'Chưa xác định',
        tinhtrang: attributes.tinhtrang || '',
        quymo_ct: attributes.quymo_ct || '',
        nam_xd: attributes.nam_xd || null,
        nam_sd: attributes.nam_sd || null,
        dt_dat: attributes.dt_dat || 0,
        nguyengia: attributes.nguyengia || 0,
        quytrinh_vh: attributes.quytrinh_vh || '',
        quytrinh_bt: attributes.quytrinh_bt || '',
        dv_quanly: attributes.dv_quanly || '',
        phuongthuc: attributes.phuongthuc || '',
        chuthich: attributes.chuthich || '',
        // Thêm tọa độ để sử dụng khi click vào hàng
        coordinates: coordinates
      }
    })

    // Lưu dữ liệu gốc để có thể khôi phục sau khi tìm kiếm
    originalRowData.value = [...mappedData]
    rowData.value = [...mappedData]

    // Tự động tạo định nghĩa cột từ dữ liệu
    columnDefs.value = generateColumnDefs(mappedData)

    // Cập nhật popup với thông tin đầy đủ
    updateMarkersWithAttributes(attributesMap)

    return true
  } catch (error) {
    console.error('Lỗi khi tải dữ liệu thuộc tính:', error)
    return false
  } finally {
    // Kết thúc loading
    isLoadingAttributes.value = false
  }
}

// Hàm cập nhật popup cho marker với thông tin đầy đủ
function updateMarkersWithAttributes(attributesMap: Map<string, any>) {
  if (!drainageMarkers) return

  // Cập nhật popup cho từng marker
  Object.keys(drainageMarkers).forEach(id => {
    const marker = drainageMarkers[id]
    const attributes = attributesMap.get(id)

    if (marker && attributes) {
      // Tạo nội dung popup với thông tin đầy đủ
      const popupContent = `
        <div class="popup-content">
          <h3 class="font-bold">${attributes.ten || ''}</h3>
          <p><strong>ID:</strong> ${id}</p>
          <p><strong>Loại:</strong> ${attributes.loai_ct || ''}</p>
          <p><strong>Quy mô:</strong> ${attributes.quymo_ct || 'Không có'}</p>
          <p><strong>Tình trạng:</strong> ${attributes.tinhtrang || ''}</p>
          <p><strong>Năm xây dựng:</strong> ${attributes.nam_xd || 'Không rõ'}</p>
          <p><strong>Đơn vị quản lý:</strong> ${attributes.dv_quanly || ''}</p>
        </div>
      `

      // Cập nhật popup
      marker.setPopupContent(popupContent)
    }
  })
}

// Hàm tính toán bounds cho một lớp dữ liệu
function getBoundsForLayer(features: any[]) {
  if (!features || features.length === 0) return null

  // Khởi tạo bounds với giá trị min/max ban đầu
  let bounds = {
    north: -90, // Vĩ độ max
    south: 90,  // Vĩ độ min
    east: -180, // Kinh độ max
    west: 180   // Kinh độ min
  }

  // Duyệt qua tất cả các điểm để tìm bounds
  features.forEach(feature => {
    const coords = feature.geometry.coordinates
    const lat = coords[1]
    const lng = coords[0]

    // Cập nhật bounds
    bounds.north = Math.max(bounds.north, lat)
    bounds.south = Math.min(bounds.south, lat)
    bounds.east = Math.max(bounds.east, lng)
    bounds.west = Math.min(bounds.west, lng)
  })

  // Tạo LatLngBounds của Leaflet
  return L.latLngBounds(
    [bounds.south, bounds.west], // Southwest corner
    [bounds.north, bounds.east]  // Northeast corner
  )
}

// Tạo biểu tượng tùy chỉnh cho cống đập
const drainageIcon = L.divIcon({
  className: 'custom-div-icon',
  html: `<div class="marker-pin drainage-marker">
           <div class="marker-icon-inner">
             <div class="drainage-symbol"></div>
           </div>
         </div>`,
  iconSize: [36, 36],
  iconAnchor: [18, 36],
  popupAnchor: [0, -36]
})

// Tạo biểu tượng tùy chỉnh cho cống đập được chọn (highlight)
const drainageHighlightIcon = L.divIcon({
  className: 'custom-div-icon',
  html: `<div class="marker-pin drainage-marker highlight-marker">
           <div class="marker-icon-inner highlight-inner">
             <div class="drainage-symbol"></div>
           </div>
         </div>`,
  iconSize: [42, 42], // Lớn hơn icon thường
  iconAnchor: [21, 42],
  popupAnchor: [0, -42]
})

// Hàm hiển thị/ẩn lớp cống đập
async function toggleDrainageLayer(show: boolean) {
  if (!map) return

  if (show) {
    if (!drainageLayer) {
      // Tạo layer group nếu chưa có
      drainageLayer = L.layerGroup().addTo(map)

      // Tải dữ liệu geometry (chỉ tải dữ liệu không gian)
      // isLoadingGeometry sẽ được tự động set trong hàm loadGeometryData
      const geoJsonData = await loadGeometryData()
      if (!geoJsonData) return

      // Thêm marker cho mỗi feature
      geoJsonData.features.forEach((feature: any) => {
        const coords = feature.geometry.coordinates
        // GeoJSON sử dụng [longitude, latitude], Leaflet sử dụng [latitude, longitude]
        const marker = L.marker([coords[1], coords[0]], { icon: drainageIcon })
          .bindPopup(`
            <div class="popup-content">
              <h3 class="font-bold">Đang tải...</h3>
              <p><strong>ID:</strong> ${feature.properties.id}</p>
            </div>
          `)

        // Lưu marker vào object để dễ tham chiếu sau này
        drainageMarkers[feature.properties.id] = marker

        // Thêm marker vào layer group
        marker.addTo(drainageLayer!)
      })

      // Tính toán bounds và zoom đến bounds
      const layerBounds = getBoundsForLayer(geoJsonData.features)
      if (layerBounds && map) {
        // Thêm padding để đảm bảo tất cả các điểm đều hiển thị đẹp
        map.fitBounds(layerBounds, {
          padding: [50, 50], // Padding [top/bottom, left/right] tính bằng pixel
          maxZoom: 15,       // Giới hạn mức zoom tối đa
          animate: true      // Hiệu ứng chuyển động khi zoom
        })
      }
    } else {
      // Nếu layer đã tồn tại, chỉ cần thêm vào map
      drainageLayer.addTo(map)

      // Zoom đến bounds của layer nếu có dữ liệu
      if (geometryData && geometryData.features) {
        const layerBounds = getBoundsForLayer(geometryData.features)
        if (layerBounds && map) {
          map.fitBounds(layerBounds, {
            padding: [50, 50],
            maxZoom: 15,
            animate: true
          })
        }
      }
    }
  } else if (drainageLayer) {
    // Ẩn layer
    drainageLayer.removeFrom(map)
  }
}

onMounted(async () => {
  if (mapContainer.value) {
    // CartoDB
    baseLayers.cartodb = L.tileLayer(
      'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
      { attribution: '&copy; <a href="https://carto.com/">CartoDB</a>' }
    )
    // Google Satellite (thông qua xyz)
    baseLayers.satellite = L.tileLayer(
      'https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
      { attribution: '&copy; <a href=\"https://www.google.com/maps\">Google</a>' }
    )

    map = L.map(mapContainer.value, {
      center: [10.7769, 106.7009], // Tọa độ mặc định (TP.HCM)
      zoom: 12,
      layers: [baseLayers.cartodb],
      zoomControl: false,
      minZoom: 10
    })

    // Không tải dữ liệu ngay khi component được mount
    // Dữ liệu sẽ được tải khi người dùng chọn checkbox hoặc mở bảng
  }
})

// Chuyển đổi lớp bản đồ nền
function switchBaseLayer(layer: string) {
  if (!map) return
  Object.values(baseLayers).forEach(l => map!.removeLayer(l))
  baseLayers[layer].addTo(map!)
  currentBase.value = layer
}

// Hiển thị/ẩn bảng dữ liệu
async function toggleDataTable() {
  // Chỉ cho phép hiển thị bảng dữ liệu khi lớp tương ứng đã được bật
  if (showDrainageLayer.value) {
    // Nếu đang đóng bảng và sắp mở
    if (!showDataTable.value) {
      // Tải dữ liệu thuộc tính nếu chưa có
      if (rowData.value.length === 0) {
        // isLoadingAttributes sẽ được tự động set trong hàm loadAttributesAndUpdateTable
        await loadAttributesAndUpdateTable()
      }
    }

    // Chuyển đổi trạng thái hiển thị bảng
    showDataTable.value = !showDataTable.value

    // Cập nhật kích thước bảng nếu đang hiển thị
    if (showDataTable.value && dataTableRef.value && dataTableRef.value.gridApi) {
      setTimeout(() => {
        if (dataTableRef.value && dataTableRef.value.gridApi) {
          dataTableRef.value.gridApi.sizeColumnsToFit()
        }
      }, 100)
    }
  }
}

// Mở modal tìm kiếm
function openSearchModal() {
  showSearchModal.value = true
  // Reset vị trí modal khi mở
  modalPosition.value = { x: 0, y: 0 }
}

// Đóng modal tìm kiếm
function closeSearchModal() {
  showSearchModal.value = false
}

// Bắt đầu kéo modal
function startDrag(event: MouseEvent) {
  isDragging.value = true
  dragStartPosition.value = {
    x: event.clientX - modalPosition.value.x,
    y: event.clientY - modalPosition.value.y
  }

  // Thêm event listeners cho việc kéo
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

// Xử lý khi đang kéo
function onDrag(event: MouseEvent) {
  if (!isDragging.value) return

  modalPosition.value = {
    x: event.clientX - dragStartPosition.value.x,
    y: event.clientY - dragStartPosition.value.y
  }
}

// Dừng kéo
function stopDrag() {
  isDragging.value = false

  // Xóa event listeners
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// Xử lý tìm kiếm đơn giản (không sử dụng)
// function onSearch() {
//   if (dataTableRef.value) {
//     dataTableRef.value.searchData(searchText.value)
//   }
// }

// Xử lý tìm kiếm nâng cao
function onAdvancedSearch() {
  if (!dataTableRef.value || !dataTableRef.value.gridApi) return

  // Kiểm tra xem có tiêu chí tìm kiếm nào được nhập không
  const hasSearchCriteria = Object.values(searchCriteria.value).some(value => value !== '')

  if (!hasSearchCriteria) {
    // Nếu không có tiêu chí nào, khôi phục dữ liệu gốc
    resetSearch()
    return
  }

  // Lọc dữ liệu dựa trên các tiêu chí tìm kiếm
  const filteredData = originalRowData.value.filter((item: any) => {
    // Kiểm tra tên
    if (searchCriteria.value.ten && !item.ten?.toLowerCase().includes(searchCriteria.value.ten.toLowerCase())) {
      return false
    }

    // Kiểm tra quy mô
    if (searchCriteria.value.quymo_ct && !item.quymo_ct?.toLowerCase().includes(searchCriteria.value.quymo_ct.toLowerCase())) {
      return false
    }

    // Kiểm tra năm xây dựng
    if (searchCriteria.value.nam_xd && item.nam_xd !== parseInt(searchCriteria.value.nam_xd)) {
      return false
    }

    // Kiểm tra nguyên giá
    if (searchCriteria.value.nguyengia && (!item.nguyengia || item.nguyengia < parseInt(searchCriteria.value.nguyengia))) {
      return false
    }

    // Kiểm tra diện tích đất
    if (searchCriteria.value.dt_dat && (!item.dt_dat || item.dt_dat < parseInt(searchCriteria.value.dt_dat))) {
      return false
    }

    return true
  })

  // Cập nhật dữ liệu trong bảng
  rowData.value = filteredData

  // Làm mới bảng để hiển thị dữ liệu đã lọc
  if (dataTableRef.value.refreshData) {
    dataTableRef.value.refreshData()
  }

  // Cập nhật hiển thị trên bản đồ nếu lớp đang được hiển thị
  if (showDrainageLayer.value && drainageLayer) {
    updateMapWithFilteredData(filteredData)
  }

  // Hiển thị thông báo số kết quả tìm thấy
  /* if (map && filteredData.length > 0) {
    const message = `<div class="text-center p-2"><strong>Tìm thấy ${filteredData.length} kết quả</strong></div>`
    L.popup()
      .setLatLng(map.getCenter())
      .setContent(message)
      .openOn(map)
  } */

  // Đóng modal sau khi tìm kiếm
  closeSearchModal()
}

// Hàm reset tìm kiếm và khôi phục dữ liệu gốc
function resetSearch() {
  // Xóa các tiêu chí tìm kiếm
  searchCriteria.value = {
    ten: '',
    quymo_ct: '',
    nam_xd: '',
    nguyengia: '',
    dt_dat: ''
  }

  // Khôi phục dữ liệu gốc
  if (originalRowData.value.length > 0) {
    rowData.value = [...originalRowData.value]

    // Làm mới bảng
    if (dataTableRef.value && dataTableRef.value.refreshData) {
      dataTableRef.value.refreshData()
    }

    // Khôi phục hiển thị trên bản đồ nếu lớp đang được hiển thị
    if (showDrainageLayer.value && drainageLayer) {
      updateMapWithFilteredData(originalRowData.value)
    }
  }

  // Hiển thị thông báo
 /*  if (map) {
    L.popup()
      .setLatLng(map.getCenter())
      .setContent('<div class="text-center p-2"><strong>Đã xóa bộ lọc tìm kiếm</strong></div>')
      .openOn(map)
  } */
}

// Hàm cập nhật bản đồ với dữ liệu đã lọc
function updateMapWithFilteredData(filteredData: any[]) {
  if (!map || !drainageLayer) return

  // Xóa tất cả marker hiện tại
  drainageLayer.clearLayers()
  drainageMarkers = {}

  // Thêm marker mới cho dữ liệu đã lọc
  filteredData.forEach((item: any) => {
    if (!item.coordinates) return

    const coords = item.coordinates
    // GeoJSON sử dụng [longitude, latitude], Leaflet sử dụng [latitude, longitude]
    const marker = L.marker([coords[1], coords[0]], { icon: drainageIcon })
      .bindPopup(`
        <div class="popup-content">
          <h3 class="font-bold">${item.ten}</h3>
          <p><strong>ID:</strong> ${item.id}</p>
          <p><strong>Loại:</strong> ${item.loai}</p>
          <p><strong>Quy mô:</strong> ${item.quymo_ct || 'Không có'}</p>
          <p><strong>Tình trạng:</strong> ${item.tinhtrang}</p>
          <p><strong>Năm xây dựng:</strong> ${item.nam_xd || 'Không rõ'}</p>
          <p><strong>Đơn vị quản lý:</strong> ${item.dv_quanly}</p>
        </div>
      `)

    // Lưu marker vào object để dễ tham chiếu sau này
    drainageMarkers[item.id] = marker

    // Thêm marker vào layer group (đã kiểm tra null ở đầu hàm)
    marker.addTo(drainageLayer!)
  })

  // Hiển thị thông báo nếu không có kết quả
  if (filteredData.length === 0) {
    if (map) {
      L.popup()
        .setLatLng(map.getCenter())
        .setContent('<div class="text-center p-2"><strong>Không tìm thấy kết quả phù hợp</strong></div>')
        .openOn(map)
    }
    return
  }

  // Zoom đến bounds của dữ liệu đã lọc
  const features = filteredData.map((item: any) => ({
    geometry: {
      coordinates: item.coordinates
    }
  }))

  const layerBounds = getBoundsForLayer(features)
  if (layerBounds && map) {
    // Thêm hiệu ứng nhấp nháy để làm nổi bật kết quả tìm kiếm
    setTimeout(() => {
      if (map) {
        map.fitBounds(layerBounds, {
          padding: [50, 50],
          maxZoom: 15,
          animate: true
        })
      }
    }, 100)
  }
}

// Hàm zoom đến dữ liệu hiện tại trên bản đồ (không sử dụng)
// function zoomToCurrentData() {
//   if (!map || rowData.value.length === 0) return
//
//   // Lấy dữ liệu từ rowData
//   const features = rowData.value.map((item: any) => ({
//     geometry: {
//       coordinates: item.coordinates
//     }
//   }))
//
//   // Tính toán bounds và zoom đến bounds
//   const layerBounds = getBoundsForLayer(features)
//   if (layerBounds && map) {
//     map.fitBounds(layerBounds, {
//       padding: [50, 50],
//       maxZoom: 15,
//       animate: true
//     })
//   }
// }

// Xử lý khi click vào hàng
function onRowClicked(event: any) {
  // Lấy dữ liệu từ hàng được chọn
  const data = event.data
  console.log('Đã chọn:', data)

  // Nếu có tọa độ, zoom đến vị trí trên bản đồ
  if (map && data.coordinates) {
    // Tạo một feature đơn giản từ dữ liệu được chọn
    const selectedFeature = {
      geometry: {
        coordinates: data.coordinates
      }
    }

    // Tạo bounds cho đối tượng được chọn
    // Thêm padding để đảm bảo đối tượng nằm ở giữa màn hình
    const bounds = getBoundsForLayer([selectedFeature])

    if (bounds) {
      // Tạo hiệu ứng nhấp nháy để làm nổi bật đối tượng được chọn
      setTimeout(() => {
        // Zoom đến bounds với padding lớn hơn để đảm bảo đối tượng nằm ở giữa màn hình
        map!.fitBounds(bounds, {
          padding: [100, 100], // Padding lớn hơn để đảm bảo đối tượng nằm ở giữa
          maxZoom: 17,        // Zoom gần hơn để thấy rõ đối tượng
          animate: true       // Hiệu ứng chuyển động khi zoom
        })

        // Mở popup nếu marker tồn tại
        if (drainageMarkers[data.id]) {
          // Đợi một chút để đảm bảo map đã zoom xong
          setTimeout(() => {
            drainageMarkers[data.id].openPopup()
          }, 300)
        }
      }, 100)

      // Làm nổi bật marker được chọn
      highlightSelectedMarker(data.id)
    }
  }
}

// Hàm làm nổi bật marker được chọn
function highlightSelectedMarker(selectedId: string) {
  // Reset tất cả marker về trạng thái bình thường trước
  Object.keys(drainageMarkers).forEach(id => {
    const marker = drainageMarkers[id]
    // Đặt lại icon mặc định
    marker.setIcon(drainageIcon)
    // Reset Z-index offset
    marker.setZIndexOffset(0)
  })

  // Làm nổi bật marker được chọn
  if (drainageMarkers[selectedId]) {
    // Sử dụng icon highlight đã định nghĩa
    drainageMarkers[selectedId].setIcon(drainageHighlightIcon)
    // Đảm bảo marker được hiển thị ở trên cùng
    drainageMarkers[selectedId].setZIndexOffset(1000)
  }
}

// Cập nhật kích thước bản đồ khi sidebar thay đổi
function updateMapSize() {
  // Đợi một chút để đảm bảo DOM đã được render
  setTimeout(() => {
    if (map) {
      map.invalidateSize()
    }
    // Cập nhật kích thước của DataTable nếu đang hiển thị
    if (showDataTable.value && dataTableRef.value && dataTableRef.value.gridApi) {
      dataTableRef.value.gridApi.sizeColumnsToFit()
    }
  }, 300)
}

// Biến để lưu trạng thái sidebar
const isSidebarOpenState = ref(true)

// Theo dõi thay đổi trạng thái sidebar
watch(isSidebarOpenState, () => {
  nextTick(() => {
    updateMapSize()
  })
})

// Theo dõi thay đổi trạng thái hiển thị lớp cống đập
watch(showDrainageLayer, (newValue) => {
  toggleDrainageLayer(newValue)
})

onMounted(() => {
  updateMapSize()
})
</script>

<template>
  <MainLayout>
    <template #default="{ isSidebarOpen }">
      <div class="relative w-full h-full text-sm" @vue:mounted="isSidebarOpenState = isSidebarOpen"
        @vue:updated="isSidebarOpenState = isSidebarOpen">
        <div class="absolute top-4 right-4 z-10 bg-white rounded shadow flex">
          <button class="px-3 py-1 rounded border cursor-pointer"
            :class="currentBase === 'cartodb' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700'"
            @click="switchBaseLayer('cartodb')">
            Địa hình
          </button>
          <button class="px-3 py-1 rounded border cursor-pointer"
            :class="currentBase === 'satellite' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700'"
            @click="switchBaseLayer('satellite')">
            Vệ tinh
          </button>
        </div>
        <div class="relative w-full h-full">
          <div ref="mapContainer" class="w-full h-full rounded z-0" style="min-height: 400px;"></div>

          <!-- Loading overlay for the map -->
          <div v-if="isLoadingGeometry || isLoadingAttributes"
               class="absolute bottom-4 right-4 bg-white p-2 rounded-md shadow-md z-20 flex items-center gap-2">
            <LoadingSpinner size="sm" color="primary" />
            <span class="text-sm font-medium">
              {{ isLoadingGeometry ? 'Đang tải dữ liệu bản đồ...' : 'Đang tải dữ liệu thuộc tính...' }}
            </span>
          </div>
        </div>

        <!-- Bảng dữ liệu -->
        <div v-if="showDataTable" class="absolute bottom-0 z-10 bg-white rounded shadow-lg transition-all duration-500"
          :class="isSidebarOpen ? 'w-[82%] ml-[18%]' : 'w-full ml-0'">
          <div class="flex justify-between items-center px-2 py-1 border-b">
            <div class="flex items-center gap-2">
              <h3 class="font-semibold">Dữ liệu tài sản cống đập</h3>
              <!-- Hiển thị spinner khi đang tải dữ liệu thuộc tính -->
              <LoadingSpinner v-if="isLoadingAttributes" size="sm" color="primary" />
            </div>
            <Button @click="resetSearch" class="flex items-center gap-1" variant="outline" size="sm">
              Xóa bộ lọc
            </Button>
            <div class="flex gap-2">
              <Button variant="outline" size="sm" class="flex items-center gap-1" @click="openSearchModal">
                <Search class="h-4 w-4" />
                <span>Tìm kiếm</span>
              </Button>
              <button @click="toggleDataTable" class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-sm">
                Đóng
              </button>
            </div>
          </div>
          <DataTable ref="dataTableRef" :columnDefs="columnDefs" :rowData="rowData" height="100%"
            locale="vi-VN" @row-clicked="onRowClicked" />
        </div>

        <!-- Modal tìm kiếm nâng cao -->
        <div v-if="showSearchModal" class="fixed inset-0 flex items-center justify-center z-50">
          <div ref="modalRef" class="bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl relative" :style="{
            transform: `translate(${modalPosition.x}px, ${modalPosition.y}px)`
          }">
            <!-- Thanh kéo (drag handle) -->
            <div class="absolute top-0 left-0 right-0 h-10 bg-gray-100 rounded-t-lg flex items-center px-4 cursor-move"
              @mousedown="startDrag">
              <GripHorizontal class="h-5 w-5 text-gray-500 mr-2" />
              <h3 class="text-lg font-semibold flex-grow">Tìm kiếm</h3>
              <button @click="closeSearchModal" class="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div class="space-y-4 mt-10">
              <!-- Grid layout cho các trường tìm kiếm -->
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Tên công trình</label>
                  <input type="text" v-model="searchCriteria.ten"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Nhập tên công trình..." />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Quy mô</label>
                  <input type="text" v-model="searchCriteria.quymo_ct"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Nhập quy mô..." />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Năm xây dựng</label>
                  <input type="number" v-model="searchCriteria.nam_xd"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Nhập năm xây dựng..." />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Nguyên giá (VNĐ)</label>
                  <input type="number" v-model="searchCriteria.nguyengia"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Nhập nguyên giá tối thiểu..." />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Diện tích đất (m²)</label>
                  <input type="number" v-model="searchCriteria.dt_dat"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Nhập diện tích tối thiểu..." />
                </div>
              </div>

              <div class="flex justify-end gap-2 mt-6">
                <Button @click="closeSearchModal"
                  class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                  Hủy
                </Button>
                <Button @click="onAdvancedSearch" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Tìm kiếm
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #sidebar>
      <div class="p-4">
        <label class="block mb-2 text-base">
          <select
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer">
            <option value="" disabled >Chọn phường/xã</option>
            <option value="all">Tất cả</option>
            <option value="phuong1">Phường Tân Định</option>
            <option value="phuong2">Thị trấn Tân Túc</option>
            <option value="phuong3">Xã Phạm Văn Hai</option>
            <option value="phuong4">Xã Bình Lợi</option>
            <option value="phuong5">Xã Tân Nhựt</option>
            <option value="phuong5">Xã Tân Kiên</option>
            <option value="phuong5">Xã Phong Phú</option>
            <option value="phuong5">Xã An Phú Tây</option>
            <option value="phuong5">Xã Hưng Long</option>
            <option value="phuong5">Xã Đa Phước</option>
            <option value="phuong5">Xã Tân Quý Tây</option>
            <option value="phuong5">Xã Quy Đức</option>
          </select>
        </label>
        <!-- Accordion Sidebar -->
        <div>
          <!-- Bản đồ nền -->
          <details class="mb-2 text-base" open>
            <summary class="cursor-pointer font-semibold text-gray-800 py-2">Bản đồ nền</summary>
            <div class="pl-4 flex flex-col gap-2">
              <label class="flex items-center gap-2 cursor-pointer">
                <input type="checkbox" class="accent-blue-600" />
                <span>Giao thông</span>
              </label>
              <label class="flex items-center gap-2 cursor-pointer">
                <input type="checkbox" class="accent-blue-600" />
                <span>Thuỷ hệ</span>
              </label>
            </div>
          </details>
          <!-- Bản đồ chuyên đề -->
          <details class="mb-2 text-base">
            <summary class="cursor-pointer font-semibold text-gray-800 py-2">Bản đồ chuyên đề</summary>
            <div class="pl-4 flex flex-col gap-2">
              <label class="flex items-center gap-2 cursor-pointer">
                <input type="checkbox" v-model="showDrainageLayer" class="accent-blue-600 flex-shrink-0" />
                <div class="flex items-center justify-between w-full">
                  <div class="flex items-center gap-2">
                    <span>Cống đập</span>
                    <!-- Hiển thị spinner khi đang tải dữ liệu geometry -->
                    <LoadingSpinner v-if="isLoadingGeometry" size="sm" color="primary" />
                  </div>
                  <div class="flex">
                    <!-- Nút hiển thị bảng dữ liệu -->
                    <Button variant="ghost" size="icon" class="h-7 w-7 flex-shrink-0 cursor-pointer relative"
                      @click="toggleDataTable" :disabled="!showDrainageLayer"
                      :class="{ 'opacity-50 cursor-not-allowed': !showDrainageLayer }">
                      <TableProperties />
                      <!-- Hiển thị spinner khi đang tải dữ liệu thuộc tính -->
                      <LoadingSpinner v-if="isLoadingAttributes" size="sm" color="primary"
                        class="absolute -top-1 -right-1" />
                      <span class="sr-only">Toggle DataTable</span>
                    </Button>
                  </div>
                </div>
              </label>
              <label class="flex items-center gap-2">
                <input type="checkbox" v-model="showPumpStationLayer" class="accent-blue-600 flex-shrink-0" />
                <div class="flex items-center justify-between w-full">
                  <span>Trạm bơm</span>
                  <Button variant="ghost" size="icon" class="h-7 w-7 flex-shrink-0" :disabled="!showPumpStationLayer"
                    :class="{ 'opacity-50 cursor-not-allowed': !showPumpStationLayer }">
                    <TableProperties />
                    <span class="sr-only">Toggle DataTable</span>
                  </Button>
                </div>
              </label>
              <label class="flex items-center gap-2">
                <input type="checkbox" v-model="showDikeLayer" class="accent-blue-600 flex-shrink-0" />
                <div class="flex items-center justify-between w-full">
                  <span>Đê bao bờ bao</span>
                  <Button variant="ghost" size="icon" class="h-7 w-7 flex-shrink-0" :disabled="!showDikeLayer"
                    :class="{ 'opacity-50 cursor-not-allowed': !showDikeLayer }">
                    <TableProperties />
                    <span class="sr-only">Toggle DataTable</span>
                  </Button>
                </div>
              </label>
              <label class="flex items-center gap-2">
                <input type="checkbox" v-model="showCanalLayer" class="accent-blue-600 flex-shrink-0" />
                <div class="flex items-center justify-between w-full">
                  <span>Kênh mương</span>
                  <Button variant="ghost" size="icon" class="h-7 w-7 flex-shrink-0" :disabled="!showCanalLayer"
                    :class="{ 'opacity-50 cursor-not-allowed': !showCanalLayer }">
                    <TableProperties />
                    <span class="sr-only">Toggle DataTable</span>
                  </Button>
                </div>
              </label>
              <label class="flex items-center gap-2">
                <input type="checkbox" v-model="showOtherAssetsLayer" class="accent-blue-600 flex-shrink-0" />
                <div class="flex items-center justify-between w-full">
                  <span>Tài sản khác</span>
                  <Button variant="ghost" size="icon" class="h-7 w-7 flex-shrink-0" :disabled="!showOtherAssetsLayer"
                    :class="{ 'opacity-50 cursor-not-allowed': !showOtherAssetsLayer }">
                    <TableProperties />
                    <span class="sr-only">Toggle DataTable</span>
                  </Button>
                </div>
              </label>
            </div>
          </details>
        </div>
      </div>
    </template>
  </MainLayout>
</template>

<style>
/* Custom marker styles */
.custom-div-icon {
  background: none;
  border: none;
}

.marker-pin {
  width: 36px;
  height: 36px;
  border-radius: 50% 50% 50% 0;
  background: #0ea5e9;
  position: absolute;
  transform: rotate(-45deg);
  left: 50%;
  top: 50%;
  margin: -18px 0 0 -18px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(0,0,0,0.3);
}

.marker-pin::after {
  content: '';
  width: 24px;
  height: 24px;
  margin: 6px 0 0 6px;
  background: white;
  position: absolute;
  border-radius: 50%;
}

.marker-icon-inner {
  width: 18px;
  height: 18px;
  background-color: #0369a1;
  border-radius: 50%;
  position: relative;
  z-index: 1;
  transform: rotate(45deg);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Drainage marker specific styles */
.drainage-marker {
  background: #0284c7;
}

.drainage-marker .marker-icon-inner {
  background-color: #0369a1;
}

/* Symbol for drainage structures */
.drainage-symbol {
  width: 10px;
  height: 10px;
  position: relative;
  overflow: hidden;
}

.drainage-symbol::before,
.drainage-symbol::after {
  content: '';
  position: absolute;
  background-color: white;
}

.drainage-symbol::before {
  width: 10px;
  height: 2px;
  top: 4px;
  left: 0;
}

.drainage-symbol::after {
  width: 2px;
  height: 10px;
  top: 0;
  left: 4px;
}

/* Highlight marker styles */
.highlight-marker {
  background: #f59e0b; /* Amber color for highlight */
  box-shadow: 0 3px 14px rgba(0,0,0,0.5); /* Stronger shadow */
  animation: pulse 1.5s infinite; /* Pulsing animation */
}

.highlight-inner {
  background-color: #d97706; /* Darker amber for inner circle */
}

/* Pulsing animation for highlighted marker */
@keyframes pulse {
  0% {
    transform: rotate(-45deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: rotate(-45deg) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: rotate(-45deg) scale(1);
    opacity: 1;
  }
}
</style>
